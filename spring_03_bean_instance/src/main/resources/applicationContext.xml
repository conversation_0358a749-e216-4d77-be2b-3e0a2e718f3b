<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--方式一：构造方法实例化bean-->
<!--    <bean id="bookDao" class="com.itheima.dao.impl.BookDaoImpl"/>-->


    <!--方式二：使用静态工厂实例化bean-->
<!--    <bean id="orderDao" class="com.itheima.factory.OrderDaoFactory" factory-method="getOrderDao"/>-->

    <!--方式三：使用实例工厂实例化bean-->
<!--        <bean id="userFactory" class="com.itheima.factory.UserDaoFactory"/>-->

<!--        <bean id="userDao" factory-method="getUserDao" factory-bean="userFactory"/>-->

    <!--方式四：使用FactoryBean实例化bean-->
    <bean id="userDao" class="com.itheima.factory.UserDaoFactoryBean"/>

</beans>
